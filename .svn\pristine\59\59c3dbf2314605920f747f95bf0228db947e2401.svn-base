<?php
/**
 * Hachu_PdfSendController
 *
 * 発注処理 Email 送信クラス
 * 
 * @category   App
 * @package    controller\hachu
 * <AUTHOR> Sato
 * @since      2014/xx/xx
 * @since      2015/06/03 MSI Mihara mail(fax)送信状況を把握できるよう改善
                          (1)ログ出力(異常,正常) (2)<EMAIL> はエラー (3)maillog の監視(別途)
 * @since      2015/12/18 MSI Sai efax→faximosilver 変更対応
 * @filesource 
 */

/**
 * 発注処理 Email 送信クラス
 *
 * @category   App
 * @package    controller\hachu
 * <AUTHOR> Sato
 * @since      2014/xx/xx
  */
class Hachu_PdfSendController extends Zend_Controller_Action
{
    const ERR_STATUS_UNABLESEND = 1;	// 送信できない
    const ERR_STATUS_UNABLESESSIONDATA = 2;	// セッションデータを取得できない
    const ERR_STATUS_UNABLEMAKEDENPYO = 3;	// 伝票を作成できない

	/**
     * アクション
     *
     * <AUTHOR> Sato
     * @since 2014/03/26
     */
	public function indexAction()
    {
        $params  = Msi_Sys_Utils::webInputs();
		$info_json = Msi_Sys_Utils::json_decode($params['info_json']);
		$info_ary = $info_json['data'];
		$option = isset($info_json['option']) ? $info_json['option'] : array();

		$mode = array_key_exists('mode', $option) ? $option['mode'] : 'normal';
		if ($mode == 'normal') {
			// 通常の発注処理
			foreach ($info_ary as $info) {
				$hachu_time = $info['hachu_time'];
				$seko_no = $info['seko_no'];
				$hachu_no_ary = $info['hachu_no_ary'];
				$key = $info['key'];

				$db = Msi_Sys_DbManager::getMyDb();
				$hachu_no = $hachu_no_ary[0];

				// 施行発注管理情報を取得
				$rec = DataMapper_SekoHachuInfo::find($db, array("seko_no" => $seko_no, "hachu_no" => $hachu_no));
				if (count($rec) == 0) { continue; }
				$hachu = $rec[0];
                                
				//花輪
				if (($rec[0]["ha_rp_kbn"] == "17") || ($rec[0]["ha_rp_kbn"] == "18")){
					$rec_moto = DataMapper_SekoHachuInfo::find($db, array("seko_no" => $seko_no, "hachu_no" => $rec[0]["hachu_no_moto"] ));
					if (count($rec_moto) > 0){
						$hachu_no_ary2[] = $rec_moto[0]["hachu_no"];
						App_HachuLib::setOrderEnd($seko_no, $hachu_no_ary2);  //元データを発注済みにする
					}
				}

				//おくやみ記事の場合
				if ($rec[0]["ha_rp_kbn"] == "2"){
					$hachu_no_ary = $this->getHachuNos($seko_no, $hachu['siire_cd']);
				}
				//オリジナル礼状の場合
				if ($rec[0]["ha_rp_kbn"] == "7"){
					$hachu_no_ary = $this->getHachuNos2($seko_no, $rec[0]["ha_rp_kbn"]);
				}
				// 加工依頼書の場合
				if ($rec[0]["ha_rp_kbn"] == "4"){
					$hachu_no_ary_tmp = $this->getHachuNos3($seko_no, $rec[0]["ha_rp_kbn"], $hachu_no);
					if (count($hachu_no_ary_tmp) > 0) {
						$hachu_no_ary = $hachu_no_ary_tmp;
					}
				}
                // 駐車場警備業務依頼書の場合
                if ($rec[0]["ha_rp_kbn"] == "36") {
                    $rec_all_free = DataMapper_SekoKihonAllFree::find($db, array("seko_no" => $seko_no));
                    $siireM = DataMapper_Siire::find($db, array("siire_cd" => $rec_all_free[0]['v_free19']));
                    if (count($siireM) == 1) {
                        $hachu['siire_cd'] = $siireM[0]['siire_cd'];
                    } else {
                        $this->_send_err_msg_dtl = '仕入先が設定されていません';
						$this->err(self::ERR_STATUS_UNABLESEND);
                        return;
                    }
                }
                                
				// FAX、メール
				if ($hachu['ha_syori_kbn'] == 1 || $hachu['ha_syori_kbn'] == 2) {
					$data = Msi_Sys_Utils::getSessionVar($key);
					if ( $data === null ) {
						$this->err(self::ERR_STATUS_UNABLESESSIONDATA);
						return;
	//					throw new Exception("getMyTempFile: key error")
					}
					// 送信
					$kaisya_cd =  App_Utils::getCtxtKaisyaEasy();		// 2017/01/25 ADD Kayo
					$rec = DataMapper_Siire::find($db, array('siire_cd' => $hachu['siire_cd'], 'kaisya_cd' => $kaisya_cd));	// 2017/01/25 ADD Kayo
					if (count($rec) == 0) { continue; }
					$siire = $rec[0];
                    if (isset($siire['fax_haishin_kbn']) && ($siire['fax_haishin_kbn'] == 0 || $siire['fax_haishin_kbn'] == 1)) {
                        $ret = $this->send($seko_no, $hachu['siire_cd'], $hachu_time, $hachu['report_cd'], $siire['fax_haishin_kbn'], $data['contFile'], $data['filename']);
                        if (!$ret) {
                            $this->err(self::ERR_STATUS_UNABLESEND);
                            return;
                        }
                    }
				}

				// 発注済みに設定
				App_HachuLib::setOrderEnd($seko_no, $hachu_no_ary);

				// 発注伝票を作成
				$ret = App_HachuLib::makeDenpyo($seko_no, $hachu_no_ary);
				if (!$ret) {
					$this->err(self::ERR_STATUS_UNABLEMAKEDENPYO);
					return;
				}
			}
		} else {
			$db = Msi_Sys_DbManager::getMyDb();

			// 発注伝票からの発注処理
			foreach ($info_ary as $info) {
				$hachu_time = $info['hachu_time'];
				$denpyo_no = $info['denpyo_no'];
				$siire_cd = $info['siire_cd'];
				$key = $info['key'];

				// 仕入先を取得
				$kaisya_cd =  App_Utils::getCtxtKaisyaEasy();
				$rec = DataMapper_Siire::find($db, array('siire_cd' => $siire_cd, 'kaisya_cd' => $kaisya_cd));
				if (count($rec) == 0) { continue; }
				$siire = $rec[0];
                // ナウエル、彩苑の場合、配信区分が設定されていないので、2017/01/27 ADD Kayo
                if (strlen($siire['fax_haishin_kbn']) <= 0) {
                   $siire['fax_haishin_kbn'] = 0; 
                }

				// FAX、メール
				if ($siire['fax_haishin_kbn'] == 0 || $siire['fax_haishin_kbn'] == 1) {
					$data = Msi_Sys_Utils::getSessionVar($key);
					if ( $data === null ) {
						$this->err(self::ERR_STATUS_UNABLESESSIONDATA);
						return;
					}
					// 送信
					$ret = $this->send('0000000000', $siire['siire_cd'], $hachu_time, '00020', $siire['fax_haishin_kbn'], $data['contFile'], $data['filename']);
					if (!$ret) {
						$this->err(self::ERR_STATUS_UNABLESEND);
						return;
					}
				}

				// 発注済みに設定
				DataMapper_HachuCommon::setDenpyoOrderEnd($db, $denpyo_no, $hachu_time);

			}
		}

        //$this->_helper->viewRenderer->setNoRender();
        // 閉じる
//        Msi_Sys_Utils::outHtml('<script>window.open("about:blank","_self").close();</script>'); // 確認ダイアログを表示しない
        Msi_Sys_Utils::outJson( array('status' => 'OK') );
    }

	private function send($seko_no, $siire_cd, $hachu_time, $report_cd, $teikei_kbn, $filepath, $filename)
	{
		// とりあえず、定型文コードは1で渡す
		$mail = App_FaxSend::getHachuMailInfo($seko_no, $siire_cd, $hachu_time, $report_cd, $teikei_kbn, 1);
//			if ($minus) {
//				$body = '○○家の発注を取り消します。';
//			}
		if (isset($mail)) {
            if ( !isset($mail['to']) || strlen($mail['to']) <= 0 || preg_match('/^@cl1.faximo.jp/', $mail['to']) ) {
                // 2015/06/03 mihara added
                @ Msi_Sys_Utils::warn( sprintf('Hachu_PdfSendController: 宛先アドレス不正(%s), 施行No(%s), 仕入CD(%s), filepath(%s)',
                                               $mail['to'], $seko_no, $siire_cd, basename($filepath)) );
                if ( preg_match('/^@cl1.faximo.jp/', $mail['to']) ) {
                    $this->_send_err_msg_dtl = 'FAX番号が設定されていません';
                } else {
                    $this->_send_err_msg_dtl = 'メールアドレスが設定されていません';
                }
                return false;
            }
            try {
                App_FaxSend::send($mail['to'], $mail['title'], $mail['body'], $filepath, $filename, $mail['from'], $mail['replyTo']);
            } catch ( Exception $e ) { // 2015/06/03 mihara added
                $this->_send_err_msg_dtl = $e->getMessage();
                return false;
            }
            if ( Msi_Sys_Utils::getExConfigByKeyDefault( 'msi.library.mail.enable', true ) ) { // 2015/06/03 mihara added
                // 実際に送った場合にはログに書く
                Msi_Sys_Utils::info( sprintf('発注メール送信しました. 宛先(%s), 施行No(%s), 仕入CD(%s), filepath(%s)',
                                             $mail['to'], $seko_no, $siire_cd, basename($filepath)) );
            }
			return true;
		}
		return false;
	}

    private function err($status) {
		$msg = $this->getErrMsg($status);
        Msi_Sys_Utils::err( 'Hachu_PdfSendController: ' . $msg ); // 2015/06/03 mihara added
        Msi_Sys_Utils::outJson( array('status' => 'NG', 'msg' => $msg) );
//        Msi_Sys_Utils::pushMsg( $msg, 'err' );
//        Msi_Sys_Utils::outHtml('<script>history.back();</script>');
    }

	private function getErrMsg($status)
	{
        switch ($status) {
            case self::ERR_STATUS_UNABLESEND:
                $msg = '送信できません['.$status.']';
                break;
            case self::ERR_STATUS_UNABLESESSIONDATA:
                $msg = 'getMyTempFile: key error['.$status.']';
                break;
			case self::ERR_STATUS_UNABLEMAKEDENPYO:
                $msg = '伝票を作成できません['.$status.']';
                break;
            default:
                $msg = 'エラー['.$status.']';
        }
        if ( isset($this->_send_err_msg_dtl) && strlen($this->_send_err_msg_dtl) ) { // 2015/06/03 mihara added
            $msg .= "\n" . $this->_send_err_msg_dtl;
        }
		return $msg;
	}
    private function getHachuNos($seko_no, $siire_cd){
        $db = Msi_Sys_DbManager::getMyDb();
        $hachuno = array();
        $sql= "select hachu_no
               from seko_hachu_info
               where seko_no=:seko_no
                 and siire_cd=:siire_cd
                 and ha_rp_kbn=2
                 and ha_etc_kbn=0
                 and status_kbn in (0,1)
                 and delete_flg= 0";
        $select = $db->easySelect($sql, array('seko_no' => $seko_no, 'siire_cd' => $siire_cd));

        foreach($select as $sel) {
            $hachuno[]=$sel["hachu_no"];
        }
        
        return $hachuno;
        
    }
    
    /**
     *
     * 発注番号取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/12/2
     * @return array 発注番号配列
     */
    private function getHachuNos2($seko_no, $ha_rp_kbn){
        $db = Msi_Sys_DbManager::getMyDb();
        $hachuno = array();
        $sql= "select hachu_no
               from seko_hachu_info
               where seko_no=:seko_no
                 and ha_rp_kbn=:ha_rp_kbn
                 and ha_etc_kbn=0
                 and status_kbn in (0,1)
                 and delete_flg= 0";
        $select = $db->easySelect($sql, array('seko_no' => $seko_no, 'ha_rp_kbn' => $ha_rp_kbn));

        foreach($select as $sel) {
            $hachuno[]=$sel["hachu_no"];
        }
        
        return $hachuno;
        
    }
    
    /**
     *
     * 加工依頼書の発注番号取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/12/25
     * @return array 発注番号配列
     */
    private function getHachuNos3($seko_no, $ha_rp_kbn, $hachu_no) {
        $db = Msi_Sys_DbManager::getMyDb();
        $hachuno = array();
        $sql1 = "select k_free1
               from seko_hachu_info
               where seko_no=:seko_no
                 and hachu_no=:hachu_no
                 and ha_etc_kbn=0
                 and status_kbn in (0,1)
                 and delete_flg= 0";
        $select1 = $db->easySelOne($sql1, array('seko_no' => $seko_no, 'hachu_no' => $hachu_no));
        if (count($select1) > 0) {
            $sql2 = "select hachu_no
               from seko_hachu_info
               where seko_no=:seko_no
                 and ha_rp_kbn=:ha_rp_kbn
                 and k_free1=:k_free1
                 and ha_etc_kbn=0
                 and status_kbn in (0,1)
                 and delete_flg= 0";
            $select2 = $db->easySelect($sql2, array('seko_no' => $seko_no, 'ha_rp_kbn' => $ha_rp_kbn, 'k_free1' => $select1['k_free1']));

            foreach ($select2 as $sel) {
                $hachuno[] = $sel["hachu_no"];
            }
        }
        return $hachuno;
    }

}